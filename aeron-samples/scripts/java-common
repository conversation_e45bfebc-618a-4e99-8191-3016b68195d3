#!/usr/bin/env bash
##
## Copyright 2014-2023 Real Logic Limited.
##
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
## https://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
##

if [ -z "${JAVA_HOME}" ]; then
  echo "Please set the JAVA_HOME environment variable"
  exit 1
fi

scripts_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"

VERSION=$(cat "$scripts_dir/../../version.txt")

JAVA_OPTIONS="\
  -XX:+UnlockExperimentalVMOptions \
  -XX:+TrustFinalNonStaticFields \
  -XX:+UnlockDiagnosticVMOptions \
  -XX:GuaranteedSafepointInterval=300000 \
  -XX:+UseParallelGC"

java_version=$("${JAVA_HOME}/bin/java" -version 2>&1 | awk -F '"' '/version/ {print $2}')

if [[ ${java_version} == '1.8.'* ]]
then
  ADD_OPENS=""
else
  major_java_version=${java_version%%[.-]*}

  if [ ${major_java_version} -lt 15 ]
  then
    JAVA_OPTIONS="${JAVA_OPTIONS} -XX:+UseBiasedLocking -XX:BiasedLockingStartupDelay=0"
  fi

  ADD_OPENS="\
  --add-opens java.base/sun.nio.ch=ALL-UNNAMED \
  --add-opens java.base/java.util.zip=ALL-UNNAMED"
fi

JAVA_OPTIONS="${JAVA_OPTIONS} ${ADDITIONAL_JAVA_OPTIONS}"