#!/usr/bin/env bash
##
## Copyright 2014-2023 Real Logic Limited.
##
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
## https://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
##

PORT_TO_FILTER=20121

sudo tc qdisc del dev lo root
sudo tc qdisc add dev lo root handle 1: prio
sudo tc filter add dev lo protocol ip parent 1: prio 1 u32 match ip dport ${PORT_TO_FILTER} 0xffff match ip protocol 17 0xff flowid 1:1
sudo tc qdisc add dev lo parent 1:1 handle 20: netem loss 5% delay 10ms
tc qdisc show dev lo
