#!/usr/bin/env bash
##
## Copyright 2014-2023 Real Logic Limited.
##
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
## https://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
##

source ./script-common

# tag::start_jvm[]
function startNode() {
    ${JAVA_HOME}/bin/java \
        -cp ../../../aeron-all/build/libs/aeron-all-${VERSION}.jar \
        -XX:+UseBiasedLocking \
        -XX:BiasedLockingStartupDelay=0 \
        -XX:+UnlockExperimentalVMOptions \
        -XX:+TrustFinalNonStaticFields \
        -XX:+UnlockDiagnosticVMOptions \
        -XX:GuaranteedSafepointInterval=300000 \
        -XX:+UseParallelGC \
        -Daeron.cluster.tutorial.customerId="${1}" \
        -Daeron.cluster.tutorial.numOfBids="${2}" \
        -Daeron.cluster.tutorial.bidIntervalMs="${3}" \
        ${JVM_OPTS} ${ADD_OPENS} \
        io.aeron.samples.cluster.tutorial.BasicAuctionClusterClient
}

if [[ $# -lt 1 ]]; then
  echo "Usage: ./basic-action-client <customer id> [number of bids (default 10)] [bid interval ms (default 1000)]"
  exit 1
fi

CUSTOMER_ID=${1}
NUM_OF_BIDS=${2:-10}
BID_INTERVAL_MS=${3:-1000}

startNode ${CUSTOMER_ID} ${NUM_OF_BIDS} ${BID_INTERVAL_MS}
# end::start_jvm[]

