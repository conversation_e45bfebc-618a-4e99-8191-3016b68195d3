#!/usr/bin/env bash

##
## Copyright 2014-2023 Real Logic Limited.
##
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
## https://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
##

if [ -z "${JAVA_HOME}" ]; then
  echo "Please set the JAVA_HOME environment variable"
  exit 1
fi

VERSION=$(cat ../../../version.txt)

JDK_VERSION=$(${JAVA_HOME}/bin/java -version 2>&1 | awk -F '"' '/version/ {print $2}')

if [[ ${JDK_VERSION} == '1.8.'* ]]; then
  ADD_OPENS=""
else
  ADD_OPENS="--add-opens java.base/sun.nio.ch=ALL-UNNAMED"
fi
