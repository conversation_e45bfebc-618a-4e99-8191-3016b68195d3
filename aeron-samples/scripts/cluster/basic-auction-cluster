#!/usr/bin/env bash
##
## Copyright 2014-2023 Real Logic Limited.
##
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
## https://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
##

source ./script-common

mkdir -p logs

# tag::start_jvm[]
function startNode() {
    ${JAVA_HOME}/bin/java \
        -cp ../../../aeron-all/build/libs/aeron-all-${VERSION}.jar \
        -javaagent:../../../aeron-agent/build/libs/aeron-agent-${VERSION}.jar \
        -XX:+UseBiasedLocking \
        -XX:BiasedLockingStartupDelay=0 \
        -XX:+UnlockExperimentalVMOptions \
        -XX:+TrustFinalNonStaticFields \
        -XX:+UnlockDiagnosticVMOptions \
        -XX:GuaranteedSafepointInterval=300000 \
        -XX:+UseParallelGC \
        -Daeron.event.cluster.log=all \
        -Daeron.event.cluster.log.disable=CANVASS_POSITION,APPEND_POSITION,COMMIT_POSITION \
        -Daeron.cluster.tutorial.nodeId=${1} \
        ${JVM_OPTS} ${ADD_OPENS} \
        io.aeron.samples.cluster.tutorial.BasicAuctionClusteredServiceNode > logs/cluster-${1}.log &
}

function startAll() {
  startNode 0
  startNode 1
  startNode 2

  tail -n 100 -F logs/cluster-*.log
}
# end::start_jvm[]

if [[ $# -lt 1 ]]; then
  startAll
elif [[ ${1} == 0 ]] || [[ ${1} == 1 ]] || [[ ${1} == 2 ]]; then
  echo "starting node ${1}"
  startNode ${1}
else
  echo "Usage: ./basic-auction-cluster [0,1,2]"
  exit 1
fi


