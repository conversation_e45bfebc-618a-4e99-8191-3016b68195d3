#!/usr/bin/env bash
##
## Copyright 2014-2023 Real Logic Limited.
##
## Licensed under the Apache License, Version 2.0 (the "License");
## you may not use this file except in compliance with the License.
## You may obtain a copy of the License at
##
## https://www.apache.org/licenses/LICENSE-2.0
##
## Unless required by applicable law or agreed to in writing, software
## distributed under the License is distributed on an "AS IS" BASIS,
## WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
## See the License for the specific language governing permissions and
## limitations under the License.
##

set -euox pipefail

#
# Two networks a (*********/24) and b (*********/24)
# Three nodes 0 (x.x.x.10), 1 (x.x.x.11), and 2 (x.x.x.12).
# *_<network><node>, e.g. veth_a0: virtual ethernet, network a, node 0 (**********/24).
#

sudo ip netns add ns_node0
sudo ip netns add ns_node1
sudo ip netns add ns_node2

sudo ip link add name br_a type bridge
sudo ip link set br_a up

sudo ip link add name br_b type bridge
sudo ip link set br_b up

sudo ip link add veth_a0 type veth peer name peer_a0
sudo ip link set veth_a0 netns ns_node0
sudo ip netns exec ns_node0 ip addr add **********/24 dev veth_a0
sudo ip netns exec ns_node0 ip link set veth_a0 up
sudo ip link set peer_a0 up

sudo ip link add veth_b0 type veth peer name peer_b0
sudo ip link set veth_b0 netns ns_node0
sudo ip netns exec ns_node0 ip addr add *********0/24 dev veth_b0
sudo ip netns exec ns_node0 ip link set veth_b0 up
sudo ip link set peer_b0 up

sudo ip link add veth_a1 type veth peer name peer_a1
sudo ip link set veth_a1 netns ns_node1
sudo ip netns exec ns_node1 ip addr add **********/24 dev veth_a1
sudo ip netns exec ns_node1 ip link set veth_a1 up
sudo ip link set peer_a1 up

sudo ip link add veth_b1 type veth peer name peer_b1
sudo ip link set veth_b1 netns ns_node1
sudo ip netns exec ns_node1 ip addr add *********1/24 dev veth_b1
sudo ip netns exec ns_node1 ip link set veth_b1 up
sudo ip link set peer_b1 up

sudo ip link add veth_a2 type veth peer name peer_a2
sudo ip link set veth_a2 netns ns_node2
sudo ip netns exec ns_node2 ip addr add **********/24 dev veth_a2
sudo ip netns exec ns_node2 ip link set veth_a2 up
sudo ip link set peer_a2 up

sudo ip link add veth_b2 type veth peer name peer_b2
sudo ip link set veth_b2 netns ns_node2
sudo ip netns exec ns_node2 ip addr add *********2/24 dev veth_b2
sudo ip netns exec ns_node2 ip link set veth_b2 up
sudo ip link set peer_b2 up

sudo ip link set peer_a0 master br_a
sudo ip link set peer_a1 master br_a
sudo ip link set peer_a2 master br_a
sudo ip addr add *********/24 brd + dev br_a

sudo ip link set peer_b0 master br_b
sudo ip link set peer_b1 master br_b
sudo ip link set peer_b2 master br_b
sudo ip addr add *********/24 brd + dev br_b
