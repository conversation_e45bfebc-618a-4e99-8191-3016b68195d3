::
:: Copyright 2014-2023 Real Logic Limited.
::
:: Licensed under the Apache License, Version 2.0 (the "License");
:: you may not use this file except in compliance with the License.
:: You may obtain a copy of the License at
::
:: https://www.apache.org/licenses/LICENSE-2.0
::
:: Unless required by applicable law or agreed to in writing, software
:: distributed under the License is distributed on an "AS IS" BASIS,
:: WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
:: See the License for the specific language governing permissions and
:: limitations under the License.
::

@echo off
set "DIR=%~dp0"

call "%DIR%\..\run-java" ^
    -Daeron.archive.control.channel=aeron:udp?endpoint=localhost:8010 ^
    -Daeron.archive.replication.channel=aeron:udp?endpoint=localhost:0 ^
    -Daeron.archive.control.response.channel=aeron:udp?endpoint=localhost:0 ^
    io.aeron.samples.archive.RecordedBasicPublisher