aeron.archive.dir=../../build/archive
aeron.archive.threading.mode=DEDICATED
aeron.archive.file.sync.level=0
aeron.archive.file.io.max.length=1m
aeron.archive.segment.file.length=1g
aeron.spies.simulate.connection=true
aeron.threading.mode=DEDICATED
aeron.term.buffer.sparse.file=false
aeron.mtu.length=8k
aeron.ipc.mtu.length=8k
aeron.socket.so_sndbuf=2m
aeron.socket.so_rcvbuf=2m
aeron.rcv.initial.window.length=2m
aeron.sender.idle.strategy=noop
aeron.receiver.idle.strategy=noop
aeron.conductor.idle.strategy=spin
agrona.disable.bounds.checks=true