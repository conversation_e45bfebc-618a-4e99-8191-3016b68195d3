name: Continuous Integration (Low Cadence)

on:
  workflow_dispatch:
  repository_dispatch:
    types: run-slow-tests
  schedule:
    - cron: '0 0,12 * * *'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: '-Dorg.gradle.daemon=false -Dorg.gradle.java.installations.auto-detect=false -Dorg.gradle.warning.mode=fail'
  INSTALL_COMPILER_RETRIES: '5'

permissions:
  contents: read

jobs:
  java-slow-tests:
    name: Java Slow Tests (JDK ${{ matrix.java }}, ${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        java: [ '8', '11', '17', '21-ea' ]
        os: [ 'ubuntu-22.04', 'windows-latest' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION (Linux)
        if: runner.os == 'Linux'
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=${{ matrix.java }}" >> $GITHUB_ENV
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION (Windows)
        if: runner.os == 'Windows'
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=$env:JAVA_HOME" | Out-File $env:GITHUB_ENV -Encoding utf8 -Append
          echo "BUILD_JAVA_VERSION=${{ matrix.java }}" | Out-File $env:GITHUB_ENV -Encoding utf8 -Append
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Build and Run Slow Tests with Gradle
        run: ./gradlew slowTest
        env:
          BUILD_JAVA_VERSION: ${{ matrix.java }}
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-${{ matrix.os }}-java-${{ matrix.java }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  java-javadoc:
    name: JavaDoc (JDK ${{ matrix.java }}, ${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        java: [ '8', '17' ]
        os: [ 'ubuntu-22.04' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION (Linux)
        if: runner.os == 'Linux'
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=${{ matrix.java }}" >> $GITHUB_ENV
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Build and Run Javadoc
        run: ./gradlew javadoc
        env:
          BUILD_JAVA_VERSION: ${{ matrix.java }}

  cpp-slow-tests-gcc:
    name: C++ Slow System Tests GCC ${{ matrix.version }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '12' ]
    env:
      CC: gcc-${{ matrix.version }}
      CXX: g++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo apt-get install -y g++-${{ matrix.version }} libbsd-dev uuid-dev
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors --slow-system-tests --no-system-tests --no-unit-tests
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-cpp-slow-tests-gcc-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-slow-tests-clang:
    name: C++ Slow System Tests Clang ${{ matrix.version }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '16' ]
    env:
      CC: clang-${{ matrix.version }}
      CXX: clang++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          curl --retry ${INSTALL_COMPILER_RETRIES} -L https://apt.llvm.org/llvm-snapshot.gpg.key | sudo apt-key add -
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo add-apt-repository "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-${{ matrix.version }} main"
          sudo apt-get update
          sudo apt-get install -y clang-${{ matrix.version }} libbsd-dev uuid-dev
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors --slow-system-tests --no-system-tests --no-unit-tests
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name:  crash-logs-cpp-slow-tests-clang-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-slow-tests-msvc:
    name: C++ Slow System Tests MSVC
    runs-on: windows-latest
    env:
      CC: cl
      CXX: cl
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Build
        run: cppbuild/cppbuild --slow-system-tests --no-system-tests --no-unit-tests
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-cpp-slow-tests-msvc-latest
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-sanitize-gcc:
    name: C++ Sanitise Build GCC ${{ matrix.version }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '12' ]
    env:
      CC: gcc-${{ matrix.version }}
      CXX: g++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo apt-get install -y g++-${{ matrix.version }} libbsd-dev uuid-dev
      - name: Build
        run: cppbuild/cppbuild --relwithdebinfo-build --sanitise-build --c-warnings-as-errors --cxx-warnings-as-errors --slow-system-tests
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-cpp-sanitize-gcc-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-sanitize-clang:
    name: C++ Sanitise Build Clang ${{ matrix.version }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '16' ]
    env:
      CC: clang-${{ matrix.version }}
      CXX: clang++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          curl --retry ${INSTALL_COMPILER_RETRIES} -L https://apt.llvm.org/llvm-snapshot.gpg.key | sudo apt-key add -
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo add-apt-repository "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-${{ matrix.version }} main"
          sudo apt-get update
          sudo apt-get install -y clang-${{ matrix.version }} libbsd-dev uuid-dev
      - name: Build
        run: cppbuild/cppbuild --relwithdebinfo-build --sanitise-build --c-warnings-as-errors --cxx-warnings-as-errors --slow-system-tests
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-cpp-sanitize-clang-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}
