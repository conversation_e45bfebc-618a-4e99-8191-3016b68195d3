name: Continuous Integration

on:
  workflow_dispatch:
  repository_dispatch:
    types: run-commit-tests
  push:
    branches:
      - master
      - release/**
  pull_request:
    types: [opened, synchronize]
    branches:
      - master
      - release/**

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: '-Dorg.gradle.daemon=false -Dorg.gradle.java.installations.auto-detect=false -Dorg.gradle.warning.mode=fail'
  CMAKE_VERSION: '3.14.7'
  INSTALL_COMPILER_RETRIES: '5'

permissions:
  contents: read

jobs:
  java-build:
    name: Java ${{ matrix.java }} (${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        java: [ '8', '11', '17', '21-ea' ]
        os: [ 'ubuntu-22.04', 'windows-latest' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: <PERSON>ache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION (Linux)
        if: runner.os == 'Linux'
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=${{ matrix.java }}" >> $GITHUB_ENV
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION (Windows)
        if: runner.os == 'Windows'
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=$env:JAVA_HOME" | Out-File $env:GITHUB_ENV -Encoding utf8 -Append
          echo "BUILD_JAVA_VERSION=${{ matrix.java }}" | Out-File $env:GITHUB_ENV -Encoding utf8 -Append
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup small temp file system (Linux)
        if: runner.os == 'Linux'
        run: |
          sudo mkdir -p /mnt/tmp_aeron_dir
          sudo mount -t tmpfs -o size=20M,mode=777 tmpfs /mnt/tmp_aeron_dir
      - name: Setup small temp file system (Windows)
        if: runner.os == 'Windows'
        run: |
          $current_size = (Get-PartitionSupportedSize -DiskNumber 1 -PartitionNumber 1)
          $new_size = ($current_size.SizeMax - 100 * 1024 * 1024)
          Resize-Partition -DiskNumber 1 -PartitionNumber 1 -Size ($new_size)
          New-Partition -DiskNumber 1 -Size 30MB -DriveLetter T | Format-Volume -FileSystem NTFS -Confirm:$false
          mkdir T:/tmp_aeron_dir
      - name: Build with Gradle
        run: ./gradlew -x javadoc --console=plain
      - name: Remove small temp file system (Linux)
        if: always() && runner.os == 'Linux'
        run: |
          sudo umount /mnt/tmp_aeron_dir
          sudo rm -rf /mnt/tmp_aeron_dir
      - name: Remove small temp file system (Windows)
        if: always() && runner.os == 'Windows'
        run: |
          Remove-Partition -DriveLetter T -Confirm:$false
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-${{ matrix.os }}-java-${{ matrix.java }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  java-topology-build:
    name: Java Topology Tests (JDK ${{ matrix.java }}, ${{ matrix.os }})
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        java: [ '8' ]
        os: [ 'ubuntu-22.04' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=${{ matrix.java }}" >> $GITHUB_ENV
      - name: Setup virtual network interfaces
        run: ./aeron-samples/scripts/cluster/setup-namespaces
      - name: Build aeron with Gradle
        run: ./gradlew clean assemble -x javadoc
      - name: Run execution agents
        working-directory: ./aeron-samples/scripts/cluster
        run: ./agent-ns
      - name: Get network information
        run:  |
          sudo sysctl -w net.ipv4.ip_forward=1
          sudo iptables -F FORWARD
          sudo iptables -P FORWARD ACCEPT
      - name: Run topology tests with Gradle
        run: ./gradlew topologyTest
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-topology-${{ matrix.os }}-java-${{ matrix.java }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-gcc-2004-build:
    name: C++ GCC ${{ matrix.version }} (Ubuntu 20.04)
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '6', '7', '8', '9', '10' ]
    env:
      CC: gcc-${{ matrix.version }}
      CXX: g++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo add-apt-repository "deb http://archive.ubuntu.com/ubuntu/ bionic main universe"
          sudo apt-get update
          sudo apt-get install -y g++-${{ matrix.version }} libbsd-dev uuid-dev libidn11
          curl --retry ${INSTALL_COMPILER_RETRIES} -O -L https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          tar xf cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          echo "cmake-${CMAKE_VERSION}-Linux-x86_64/bin" >> $GITHUB_PATH
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-gcc-2004-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-gcc-2204-build:
    name: C++ GCC ${{ matrix.version }} (Ubuntu 22.04)
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '11', '12' ]
    env:
      CC: gcc-${{ matrix.version }}
      CXX: g++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo apt-get install -y g++-${{ matrix.version }} libbsd-dev uuid-dev
          curl --retry ${INSTALL_COMPILER_RETRIES} -O -L https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          tar xf cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          echo "cmake-${CMAKE_VERSION}-Linux-x86_64/bin" >> $GITHUB_PATH
      - name: Setup small temp file system
        run: |
          sudo mkdir -p /mnt/tmp_aeron_dir
          sudo mount -t tmpfs -o size=20M,mode=777 tmpfs /mnt/tmp_aeron_dir
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors
      - name: Remove small temp file system
        if: always()
        run: |
          sudo umount /mnt/tmp_aeron_dir
          sudo rm -rf /mnt/tmp_aeron_dir
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-gcc-2204-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-gcc-centos-build:
    name: C++ CentOS devtoolset-${{ matrix.version }}
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '7' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Build
        run: GCC_VERSION=9 cppbuild/centos-docker-build ${{ matrix.version }}
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-gcc-centos-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-gcc-rhel-build:
    name: C++ RHEL 8
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '8' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Build
        run: cppbuild/rhel-docker-build
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-gcc-rhel-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-clang-ubuntu-2004-build:
    name: C++ Clang ${{ matrix.version }} (Ubuntu 20.04)
    runs-on: ubuntu-20.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '9', '10', '11', '12' ]
    env:
      CC: clang-${{ matrix.version }}
      CXX: clang++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          curl --retry ${INSTALL_COMPILER_RETRIES} -L https://apt.llvm.org/llvm-snapshot.gpg.key | sudo apt-key add -
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo add-apt-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-${{ matrix.version }} main"
          sudo apt-get update
          sudo apt-get install -y clang-${{ matrix.version }} libstdc++-7-dev libbsd-dev uuid-dev libidn11
          curl --retry ${INSTALL_COMPILER_RETRIES} -O -L https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          tar xf cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          echo "cmake-${CMAKE_VERSION}-Linux-x86_64/bin" >> $GITHUB_PATH
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-clang-2004-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-clang-ubuntu-2204-build:
    name: C++ Clang ${{ matrix.version }} (Ubuntu 22.04)
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '13', '14', '15', '16' ]
    env:
      CC: clang-${{ matrix.version }}
      CXX: clang++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          curl --retry ${INSTALL_COMPILER_RETRIES} -L https://apt.llvm.org/llvm-snapshot.gpg.key | sudo apt-key add -
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo add-apt-repository "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-${{ matrix.version }} main"
          sudo apt-get update
          sudo apt-get install -y clang-${{ matrix.version }} libbsd-dev uuid-dev
          curl --retry ${INSTALL_COMPILER_RETRIES} -O -L https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          tar xf cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          echo "cmake-${CMAKE_VERSION}-Linux-x86_64/bin" >> $GITHUB_PATH
      - name: Setup small temp file system
        run: |
          sudo mkdir -p /mnt/tmp_aeron_dir
          sudo mount -t tmpfs -o size=20M,mode=777 tmpfs /mnt/tmp_aeron_dir
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors
      - name: Remove small temp file system
        if: always()
        run: |
          sudo umount /mnt/tmp_aeron_dir
          sudo rm -rf /mnt/tmp_aeron_dir
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-clang-2204-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-clang-debug-build:
    name: C++ Clang ${{ matrix.version }} debug build
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        version: [ '11' ]
    env:
      CC: clang-${{ matrix.version }}
      CXX: clang++-${{ matrix.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install compiler
        run: |
          echo 'Acquire::Retries "${INSTALL_COMPILER_RETRIES}";' | sudo tee -a /etc/apt/apt.conf.d/99retries
          sudo apt-get install -y clang-${{ matrix.version }} libbsd-dev uuid-dev
          curl --retry ${INSTALL_COMPILER_RETRIES} -O -L https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          tar xf cmake-${CMAKE_VERSION}-Linux-x86_64.tar.gz
          echo "cmake-${CMAKE_VERSION}-Linux-x86_64/bin" >> $GITHUB_PATH
      - name: Build
        run: cppbuild/cppbuild --debug-build
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-clang-debug-${{ matrix.version }}
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-xcode-build:
    name: C++ Xcode (macOS)
    runs-on: macos-latest
    env:
      CC: clang
      CXX: clang++
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=${JAVA_HOME}" >> $GITHUB_ENV
          echo "BUILD_JAVA_VERSION=8" >> $GITHUB_ENV
      - name: Install CMake
        run: |
          curl --retry ${INSTALL_COMPILER_RETRIES} -O -L https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/cmake-${CMAKE_VERSION}-Darwin-x86_64.tar.gz
          tar xf cmake-${CMAKE_VERSION}-Darwin-x86_64.tar.gz
          echo "cmake-${CMAKE_VERSION}-Darwin-x86_64/CMake.app/Contents/bin" >> $GITHUB_PATH
      - name: Setup small temp file system
        run: |
          sudo diskutil eraseDisk APFS tmp_aeron_dir $(hdiutil attach -nomount ram://$((2 * 1024 * 20)))
      - name: Build
        run: cppbuild/cppbuild --c-warnings-as-errors --cxx-warnings-as-errors
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-xcode-latest
          path: ${{ steps.copy_test_logs.outputs.file }}

  cpp-msvc-build:
    name: C++ MSVC (Windows)
    runs-on: windows-latest
    env:
      CC: cl
      CXX: cl
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper-${{ hashFiles('**/gradle/wrapper/gradle-wrapper.properties') }}
      - name: Setup java 8 to run the Gradle script
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: 8
      - name: Setup BUILD_JAVA_HOME & BUILD_JAVA_VERSION
        run: |
          java -Xinternalversion
          echo "BUILD_JAVA_HOME=$env:JAVA_HOME" | Out-File $env:GITHUB_ENV -Encoding utf8 -Append
          echo "BUILD_JAVA_VERSION=8" | Out-File $env:GITHUB_ENV -Encoding utf8 -Append
      - name: Install CMake
        run: |
          $client = new-object System.Net.WebClient
          $client.DownloadFile("https://github.com/Kitware/CMake/releases/download/v$Env:CMAKE_VERSION/cmake-$Env:CMAKE_VERSION-win64-x64.zip","cmake-$Env:CMAKE_VERSION-win64-x64.zip")
          7z x cmake-$Env:CMAKE_VERSION-win64-x64.zip
          echo "cmake-$Env:CMAKE_VERSION-win64-x64/bin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8
      - name: Setup small temp file system
        run: |
          $current_size = (Get-PartitionSupportedSize -DiskNumber 1 -PartitionNumber 1)
          $new_size = ($current_size.SizeMax - 100 * 1024 * 1024)
          Resize-Partition -DiskNumber 1 -PartitionNumber 1 -Size ($new_size)
          New-Partition -DiskNumber 1 -Size 30MB -DriveLetter T | Format-Volume -FileSystem NTFS -Confirm:$false
          mkdir T:/tmp_aeron_dir
      - name: Build
        run: cppbuild/cppbuild
      - name: Remove small temp file system
        run: |
          Remove-Partition -DriveLetter T -Confirm:$false
      - name: Copy test logs
        id: copy_test_logs
        if: failure()
        run: |
          echo "::set-output name=file::build/distributions/test_logs.tbz2"
          ./gradlew tarTestLogs
      - name: Upload crash logs
        if: always() && steps.copy_test_logs.outputs.file == 'build/distributions/test_logs.tbz2'
        uses: actions/upload-artifact@v3
        with:
          name: crash-logs-msvc-latest
          path: ${{ steps.copy_test_logs.outputs.file }}
