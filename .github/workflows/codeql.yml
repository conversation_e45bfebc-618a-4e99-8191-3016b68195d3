name: "CodeQL"

on:
  workflow_dispatch:
  repository_dispatch:
    types: run-commit-tests
  push:
    branches:
      - master
      - release/**
  pull_request:
    types: [opened, synchronize]
    branches:
      - master
      - release/**

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ java, cpp ]

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          config-file: ./.github/codeql/codeql-config.yml

      - name: Autobuild
        uses: github/codeql-action/autobuild@v2
        if: ${{ matrix.language == 'java' || matrix.language == 'cpp' }}

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{ matrix.language }}"
          upload: false
          output: sarif-results

      - name: filter-sarif
        uses: advanced-security/filter-sarif@v1
        with:
          # Filter out generated and third party code.
          patterns: |
            -**/thirdparty/**
            -**/generated/**
            -**/generated-src/**
            -**/generated-test/**
            -**/_deps/**
            -**/aeron-samples/**:java/uncaught-number-format-exception
            -**/*Test.java:java/uncaught-number-format-exception
            -**/*Tool.java:java/uncaught-number-format-exception
            -**/*Debug*.java:java/uncaught-number-format-exception
          input: sarif-results/${{ matrix.language }}.sarif
          output: sarif-results/${{ matrix.language }}.sarif

      - name: Upload SARIF
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: sarif-results/${{ matrix.language }}.sarif

      # optional: for debugging the uploaded sarif
      - name: Upload loc as a Build Artifact
        uses: actions/upload-artifact@v3
        with:
          name: sarif-results
          path: sarif-results
          retention-days: 1
