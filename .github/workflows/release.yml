name: Release Documentation

on:
  release:
    types: [published]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GRADLE_OPTS: '-Dorg.gradle.daemon=false -Dorg.gradle.java.installations.auto-detect=false -Dorg.gradle.warning.mode=fail'

jobs:
  java-build:
    name: Publish Release Documentation
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        java: [ '8' ]
        os: ['ubuntu-22.04' ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Cache Gradle dependencies
        uses: actions/cache@v3
        with:
          path: ~/.gradle/caches
          key: ${{ runner.os }}-gradle-caches-${{ hashFiles('**/*.gradle') }}
          restore-keys: |
            ${{ runner.os }}-gradle-caches-
      - name: Cache Gradle wrappers
        uses: actions/cache@v3
        with:
          path: ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-wrapper
      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ matrix.java }}
      - name: Build with Gradle
        run: ./gradlew tutorialPublish -Paeron.ci.apiKey=${{ secrets.WIKI_PAGE_TOKEN }} --stacktrace
