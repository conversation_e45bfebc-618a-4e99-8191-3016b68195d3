name: "Aeron CodeQL Scanning"

queries:
  - uses: security-and-quality

query-filters:
  - exclude:
      id: java/missing-override-annotation
  - exclude:
      id: cpp/poorly-documented-function
  - exclude:
      id: cpp/unused-static-variable
  - exclude:
      id: java/unused-reference-type
  - exclude:
      id: java/unused-parameter
  - exclude:
      id: cpp/trivial-switch
  - exclude:
      id: cpp/long-switch
  - exclude:
      id: cpp/integer-used-for-enum
  - exclude:
      id: cpp/stack-address-escape
