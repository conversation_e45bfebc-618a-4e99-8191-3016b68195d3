/*
 * Copyright 2014-2023 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.aeron.test.driver;

import java.io.File;
import java.util.Map;

public interface DriverOutputConsumer
{
    default void outputFiles(String aeronDirectoryName, File stdoutFile, File stderrFile)
    {
    }

    default void exitCode(String aeronDirectoryName, int exitValue, String exitMessage)
    {
    }

    default void environmentVariables(String aeronDirectoryName, Map<String, String> environment)
    {
    }
}
