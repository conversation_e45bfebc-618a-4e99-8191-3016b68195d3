Aeron Client
===

[![Javadocs](http://www.javadoc.io/badge/io.aeron/aeron-all.svg)](http://www.javadoc.io/doc/io.aeron/aeron-all)

Aeron clients are used to communicate to the media driver for the publishing of messages via publications and consuming messages of replicated publication images via subscriptions.

Clients communicate over IPC to the media driver thus allowing the media driver to run out of process and be in a different language to the clients.