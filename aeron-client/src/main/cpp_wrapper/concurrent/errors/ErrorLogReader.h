/*
 * Copyright 2014-2023 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef AERON_CONCURRENT_ERROR_LOG_READER_H
#define AERON_CONCURRENT_ERROR_LOG_READER_H

#include <functional>
#include "util/BitUtil.h"
#include "concurrent/AtomicBuffer.h"

namespace aeron { namespace concurrent { namespace errors {

namespace ErrorLogReader {

using namespace aeron::concurrent;

typedef std::function<void(
    std::int32_t observationCount,
    std::int64_t firstObservationTimestamp,
    std::int64_t lastObservationTimestamp,
    const std::string &encodedException)> error_consumer_t;

}}}}

#endif
