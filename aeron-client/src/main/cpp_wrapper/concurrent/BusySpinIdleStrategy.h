/*
 * Copyright 2014-2023 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef AERON_BUSY_SPIN_IDLE_STRATEGY_H
#define AERON_BUSY_SPIN_IDLE_STRATEGY_H

#include "concurrent/Atomic64.h"

namespace aeron { namespace concurrent {

class BusySpinIdleStrategy
{
public:
    BusySpinIdleStrategy() = default;

    inline void idle(int workCount)
    {
        if (workCount > 0)
        {
            return;
        }

        pause();
    }

    inline void reset()
    {
    }

    inline void idle()
    {
        pause();
    }

    inline static void pause()
    {
        atomic::cpu_pause();
    }

private:
};

}}

#endif
