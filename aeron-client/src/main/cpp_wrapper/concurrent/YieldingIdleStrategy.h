/*
 * Copyright 2014-2023 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef AERON_YIELDING_IDLE_STRATEGY_H
#define AERON_YIELDING_IDLE_STRATEGY_H

#include <thread>

namespace aeron { namespace concurrent {

class YieldingIdleStrategy
{
public:
    YieldingIdleStrategy() = default;

    inline void idle(int workCount)
    {
        if (workCount > 0)
        {
            return;
        }

        std::this_thread::yield();
    }

    inline void reset()
    {
    }

    inline void idle()
    {
        std::this_thread::yield();
    }

private:
};

}}

#endif
