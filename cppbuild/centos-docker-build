#!/usr/bin/env bash

if [ -z ${GCC_VERSION} ];
then
  echo "No GCC version provided!";
  exit 1;
else
  echo "GCC_VERSION=${GCC_VERSION}"
fi

SOURCE_DIR="$(pwd)"

docker build --tag centos7-aeron \
  --build-arg USER_ID="$(id -u)" \
  --build-arg GROUP_ID="$(id -g)" \
  --build-arg GCC_VERSION="${GCC_VERSION}" \
  "${SOURCE_DIR}/cppbuild/centos"

docker run --rm --shm-size=1G --network host \
  --volume="${SOURCE_DIR}":/opt/aeron \
  --volume="$(realpath ~/.gradle)":/home/<USER>/.gradle \
  centos7-aeron
