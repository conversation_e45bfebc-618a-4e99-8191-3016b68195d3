#!/usr/bin/env bash

set -euo pipefail

SOURCE_DIR="$(pwd)"
export BUILD_DIR="${SOURCE_DIR}/cppbuild/Release"
export BUILD_CONFIG=Release
EXTRA_CMAKE_ARGS=""
BUILD_PACKAGE=false
COVERAGE_BUILD=0

ncpus=1
case "$(uname)" in
  Darwin* )
    ncpus=$(sysctl -n hw.ncpu)
    ;;
  Linux*)
    ncpus=$(lscpu -p | grep -c -E -v '^#')
    ;;
esac

while [[ $# -gt 0 ]]
do
  option="${1}"
  case ${option} in
    --c-warnings-as-errors)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DC_WARNINGS_AS_ERRORS=ON"
      echo "Enabling warnings as errors for c"
      shift
      ;;
    --cxx-warnings-as-errors)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DCXX_WARNINGS_AS_ERRORS=ON"
      echo "Enabling warnings as errors for c++"
      shift
      ;;
    -a|--build-archive-api)
      echo "Enabling building of Aeron Archive API is the default"
      shift
      ;;
    -d|--debug-build)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DCMAKE_BUILD_TYPE=Debug"
      export BUILD_DIR="${SOURCE_DIR}/cppbuild/Debug"
      export BUILD_CONFIG=Debug
      echo "Enabling debug build"
      shift
      ;;
    --relwithdebinfo-build)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DCMAKE_BUILD_TYPE=RelWithDebInfo"
      export BUILD_DIR="${SOURCE_DIR}/cppbuild/RelWithDebInfo"
      export BUILD_CONFIG=RelWithDebInfo
      echo "Enabling release with debug info build"
      shift
      ;;
    --compiler-optimization-level)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DAERON_COMPILER_OPTIMIZATION_LEVEL=${2}"
      echo "Setting compiler optimization level to: -O${2}"
      shift
      shift
      ;;
    -b|--build-aeron-driver)
      echo "Enabling building of Aeron driver is the default"
      shift
      ;;
    --no-parallel)
      ncpus=1
      echo "Disabling parallel build"
      shift
      ;;
    --parallel-cpus)
      ncpus=${2}
      shift
      shift
      ;;
    --no-tests)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DAERON_TESTS=OFF"
      echo "Disabling all tests"
      shift
      ;;
    --no-unit-tests)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DAERON_UNIT_TESTS=OFF"
      echo "Disabling unit tests"
      shift
      ;;
    --no-system-tests)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DAERON_SYSTEM_TESTS=OFF"
      echo "Disabling system tests"
      shift
      ;;
    --slow-system-tests)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DAERON_SLOW_SYSTEM_TESTS=ON"
      echo "Enabling slow system tests"
      shift
      ;;
    --sanitise-build)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DSANITISE_BUILD=ON"
      echo "Enabling sanitise build"
      shift
      ;;
    --coverage-build)
      if (hash lcov 2>/dev/null && hash genhtml 2>/dev/null); then
        EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DCOVERAGE_BUILD=ON"
        echo "Enabling coverage build"
        COVERAGE_BUILD=1
      else
        echo "lcov/genhtml not found - you need these installed to run the coverage build"
        exit
      fi
      shift
      ;;
    --gradle-wrapper)
      EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DGRADLE_WRAPPER=${2}"
      echo "Setting -DGRADLE_WRAPPER=${2}"
      shift
      shift
      ;;
    --package)
      BUILD_PACKAGE=true
      shift
      ;;
    -h|--help)
      echo "${0} [--c-warnings-as-errors] [--cxx-warnings-as-errors] [--debug-build] [--relwithdebinfo-build] [--build-aeron-driver] [--build-archive-api] [--sanitise-build] [--coverage-build] [--no-parallel] [--no-system-tests] [--slow-system-tests] [--gradle-wrapper path_to_gradle] [--package] [--help]"
      exit
      ;;
    *)
      echo "Unknown option ${option}"
      echo "Use --help for help"
      exit
      ;;
  esac
done

echo "Will make with \"-j ${ncpus}\"."

if [[ -d "${BUILD_DIR}" ]] ; then
  echo "Build directory (${BUILD_DIR}) exists, removing."
  rm -rf "${BUILD_DIR}"
fi

mkdir -p "${BUILD_DIR}"

if [[ ${COVERAGE_BUILD} -eq 1 ]] ; then
  cd "${BUILD_DIR}" || exit
  cmake -G "Unix Makefiles" ${EXTRA_CMAKE_ARGS} "${SOURCE_DIR}" && make clean && make -j ${ncpus} all && ctest -C ${BUILD_CONFIG} -j ${ncpus}
  rm -rf coverage
  mkdir -p coverage
  lcov --directory . --base-directory . --capture -o coverage/cov.info
  lcov -o coverage/cov.stripped.info --remove coverage/cov.info "/usr/include/*" "*/googletest/*" "*/test/cpp/*" "*/googlemock/*"
  genhtml coverage/cov.stripped.info --demangle-cpp -o coverage
else
  EXTRA_CMAKE_ARGS="${EXTRA_CMAKE_ARGS} -DDART_TESTING_TIMEOUT=2000"

  cd "${BUILD_DIR}" || exit
  cmake -G "CodeBlocks - Unix Makefiles" ${EXTRA_CMAKE_ARGS} "${SOURCE_DIR}"
  make clean
  make -j ${ncpus} all
  ctest -C ${BUILD_CONFIG} --output-on-failure
fi

if [ true = "${BUILD_PACKAGE}" ]
then
  (
    cd "${BUILD_DIR}"
    make package
  )
fi
