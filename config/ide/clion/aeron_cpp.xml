<code_scheme name="aeron-cpp" version="173">
  <option name="LINE_SEPARATOR" value="&#xA;" />
  <Objective-C>
    <option name="INDENT_NAMESPACE_MEMBERS" value="0" />
    <option name="KEEP_NESTED_NAMESPACES_IN_ONE_LINE" value="true" />
    <option name="NAMESPACE_BRACE_PLACEMENT" value="2" />
    <option name="FUNCTION_BRACE_PLACEMENT" value="2" />
    <option name="BLOCK_BRACE_PLACEMENT" value="2" />
    <option name="FUNCTION_NON_TOP_AFTER_RETURN_TYPE_WRAP" value="0" />
    <option name="FUNCTION_TOP_AFTER_RETURN_TYPE_WRAP" value="0" />
    <option name="FUNCTION_PARAMETERS_WRAP" value="5" />
    <option name="FUNCTION_PARAMETERS_ALIGN_MULTILINE" value="false" />
    <option name="FUNCTION_PARAMETERS_NEW_LINE_AFTER_LPAR" value="true" />
    <option name="FUNCTION_CALL_ARGUMENTS_WRAP" value="5" />
    <option name="FUNCTION_CALL_ARGUMENTS_ALIGN_MULTILINE" value="false" />
    <option name="FUNCTION_CALL_ARGUMENTS_NEW_LINE_AFTER_LPAR" value="true" />
    <option name="SHIFT_OPERATION_WRAP" value="0" />
    <option name="CLASS_CONSTRUCTOR_INIT_LIST_WRAP" value="5" />
    <option name="CLASS_CONSTRUCTOR_INIT_LIST_ALIGN_MULTILINE" value="false" />
    <option name="CLASS_CONSTRUCTOR_INIT_LIST_NEW_LINE_BEFORE_COLON" value="0" />
    <option name="CLASS_CONSTRUCTOR_INIT_LIST_NEW_LINE_AFTER_COLON" value="2" />
    <option name="SUPERCLASS_LIST_ALIGN_MULTILINE" value="false" />
    <option name="SUPERCLASS_LIST_BEFORE_COLON" value="0" />
    <option name="SUPERCLASS_LIST_AFTER_COLON" value="2" />
  </Objective-C>
  <codeStyleSettings language="CMake">
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="HTML">
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="ObjectiveC">
    <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
    <option name="BLANK_LINES_BEFORE_IMPORTS" value="0" />
    <option name="BLANK_LINES_AFTER_IMPORTS" value="0" />
    <option name="BRACE_STYLE" value="2" />
    <option name="CLASS_BRACE_STYLE" value="2" />
    <option name="ELSE_ON_NEW_LINE" value="true" />
    <option name="WHILE_ON_NEW_LINE" value="true" />
    <option name="CATCH_ON_NEW_LINE" value="true" />
    <option name="ALIGN_MULTILINE_FOR" value="false" />
    <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="false" />
    <option name="ALIGN_MULTILINE_ASSIGNMENT" value="false" />
    <option name="ALIGN_MULTILINE_TERNARY_OPERATION" value="false" />
    <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="false" />
    <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACES" value="true" />
    <option name="SPACE_AFTER_TYPE_CAST" value="false" />
    <option name="BINARY_OPERATION_WRAP" value="0" />
    <option name="TERNARY_OPERATION_WRAP" value="0" />
    <option name="KEEP_SIMPLE_METHODS_IN_ONE_LINE" value="false" />
    <option name="ARRAY_INITIALIZER_WRAP" value="5" />
    <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
    <option name="ARRAY_INITIALIZER_RBRACE_ON_NEXT_LINE" value="true" />
    <option name="IF_BRACE_FORCE" value="3" />
    <option name="DOWHILE_BRACE_FORCE" value="3" />
    <option name="WHILE_BRACE_FORCE" value="3" />
    <option name="FOR_BRACE_FORCE" value="3" />
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
      <option name="LABEL_INDENT_ABSOLUTE" value="true" />
    </indentOptions>
  </codeStyleSettings>
  <codeStyleSettings language="XML">
    <indentOptions>
      <option name="CONTINUATION_INDENT_SIZE" value="4" />
    </indentOptions>
  </codeStyleSettings>
</code_scheme>