<code_scheme name="aeron" version="173">
    <option name="OTHER_INDENT_OPTIONS">
        <value>
            <option name="CONTINUATION_INDENT_SIZE" value="4" />
        </value>
    </option>
    <option name="LINE_SEPARATOR" value="&#10;" />
    <JavaCodeStyleSettings>
        <option name="GENERATE_FINAL_LOCALS" value="true" />
        <option name="GENERATE_FINAL_PARAMETERS" value="true" />
        <option name="INSERT_OVERRIDE_ANNOTATION" value="false" />
    </JavaCodeStyleSettings>
    <codeStyleSettings language="JAVA">
        <option name="BRACE_STYLE" value="2" />
        <option name="CLASS_BRACE_STYLE" value="2" />
        <option name="METHOD_BRACE_STYLE" value="2" />
        <option name="LAMBDA_BRACE_STYLE" value="5" />
        <option name="ELSE_ON_NEW_LINE" value="true" />
        <option name="WHILE_ON_NEW_LINE" value="true" />
        <option name="CATCH_ON_NEW_LINE" value="true" />
        <option name="FINALLY_ON_NEW_LINE" value="true" />
        <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
        <option name="ALIGN_MULTILINE_RESOURCES" value="false" />
        <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACES" value="true" />
        <option name="SPACE_AFTER_TYPE_CAST" value="false" />
        <option name="PREFER_PARAMETERS_WRAP" value="true" />
        <option name="METHOD_CALL_CHAIN_WRAP" value="1" />
        <option name="KEEP_SIMPLE_LAMBDAS_IN_ONE_LINE" value="true" />
        <option name="KEEP_MULTIPLE_EXPRESSIONS_IN_ONE_LINE" value="true" />
        <option name="IF_BRACE_FORCE" value="3" />
        <option name="DOWHILE_BRACE_FORCE" value="3" />
        <option name="WHILE_BRACE_FORCE" value="3" />
        <option name="FOR_BRACE_FORCE" value="3" />
        <indentOptions>
            <option name="CONTINUATION_INDENT_SIZE" value="4" />
        </indentOptions>
    </codeStyleSettings>
</code_scheme>