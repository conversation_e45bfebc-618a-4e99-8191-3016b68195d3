<?xml version="1.0"?>
<!DOCTYPE module PUBLIC "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "https://checkstyle.org/dtds/configuration_1_3.dtd">
<module name="Checker">
    <module name="SuppressionFilter">
        <property name="file" value="${config_loc}/suppressions.xml"/>
    </module>

    <module name="SuppressWithPlainTextCommentFilter">
        <property name="offCommentFormat" value="CHECKSTYLE\:OFF\:(\w+)"/>
        <property name="onCommentFormat" value="CHECKSTYLE\:ON\:(\w+)"/>
        <property name="checkFormat" value="$1"/>
    </module>

    <module name="SuppressWarningsFilter"/>

    <module name="SeverityMatchFilter">
        <property name="severity" value="info"/>
        <property name="acceptOnMatch" value="false"/>
    </module>

    <module name="FileTabCharacter">
        <property name="eachLine" value="true"/>
    </module>

    <module name="LineLength">
        <property name="max" value="120"/>
        <property name="ignorePattern" value="^[ \t]*\*.*@.*$"/>
    </module>

    <module name="RegexpHeader">
        <property name="header" value="/*\n * Copyright \d{4}(-\d{4})? (Real Logic|Adaptive Financial Consulting) Limited."/>
        <property name="fileExtensions" value="java"/>
    </module>

    <module name="TreeWalker">
        <property name="tabWidth" value="4"/>
        <property name="severity" value="error"/>

        <module name="SuppressWarningsHolder"/>

        <module name="MissingJavadocType"/>
        <module name="MissingJavadocMethod"/>
        <module name="MissingJavadocPackage"/>

        <module name="Indentation">
            <property name="forceStrictCondition" value="true"/>
        </module>

        <module name="ConstantName"/>

        <module name="FinalParameters">
            <property name="tokens" value="METHOD_DEF, CTOR_DEF, LITERAL_CATCH, FOR_EACH_CLAUSE"/>
        </module>

        <module name="FinalLocalVariable">
            <property name="validateEnhancedForLoopVariable" value="true"/>
        </module>

        <module name="LocalFinalVariableName"/>

        <module name="LocalVariableName"/>

        <module name="MemberName">
            <property name="format" value="^[a-z][a-zA-Z0-9_]*$"/>
        </module>

        <module name="MethodName"/>

        <module name="PackageName"/>

        <module name="ParameterName"/>

        <module name="StaticVariableName"/>

        <module name="TypeName">
            <property name="format" value="^[A-Z][a-zA-Z0-9_]*$"/>
        </module>

        <module name="RedundantImport"/>

        <module name="UnusedImports"/>

        <module name="MethodLength">
            <property name="tokens" value="METHOD_DEF"/>
            <property name="max" value="100"/>
        </module>

        <module name="EmptyForInitializerPad"/>

        <module name="MethodParamPad"/>

        <module name="NoWhitespaceBefore"/>

        <module name="WhitespaceAfter">
            <property name="tokens" value="COMMA, SEMI, LITERAL_IF, LITERAL_ELSE, LITERAL_WHILE, LITERAL_DO, LITERAL_FOR, DO_WHILE"/>
        </module>

        <module name="NoWhitespaceAfter">
            <property name="tokens" value="INC, DEC, UNARY_MINUS, UNARY_PLUS, BNOT, LNOT, DOT, TYPECAST, ARRAY_DECLARATOR, INDEX_OP, METHOD_REF"/>
            <property name="allowLineBreaks" value="false"/>
        </module>

        <module name="WhitespaceAround">
            <property name="allowEmptyLambdas" value="true"/>
        </module>

        <module name="SingleSpaceSeparator"/>

        <module name="OperatorWrap">
            <property name="option" value="eol"/>
        </module>

        <module name="NeedBraces"/>

        <module name="ParenPad"/>

        <module name="TypecastParenPad"/>

        <module name="ModifierOrder"/>

        <module name="RedundantModifier"/>

        <module name="NestedTryDepth">
            <property name="max" value="2"/>
        </module>

        <module name="CovariantEquals"/>

        <module name="LeftCurly">
            <property name="option" value="nl"/>
        </module>

        <module name="RightCurly">
            <property name="option" value="alone"/>
            <property name="tokens" value="LITERAL_TRY, LITERAL_CATCH, LITERAL_FINALLY, LITERAL_IF, LITERAL_ELSE, CLASS_DEF, METHOD_DEF, CTOR_DEF, LITERAL_FOR, LITERAL_WHILE, LITERAL_DO, STATIC_INIT, INSTANCE_INIT"/>
        </module>

        <module name="EmptyStatement"/>

        <module name="EqualsHashCode"/>

        <module name="DefaultComesLast"/>

        <module name="SimplifyBooleanExpression"/>

        <module name="SimplifyBooleanReturn"/>

        <module name="StringLiteralEquality"/>

        <module name="PackageDeclaration"/>

        <module name="FallThrough"/>

        <module name="FinalClass"/>

        <module name="MutableException"/>

        <module name="EmptyLineSeparator">
            <property name="allowNoEmptyLineBetweenFields" value="true"/>
            <property name="tokens" value="IMPORT, CLASS_DEF, INTERFACE_DEF, ENUM_DEF, STATIC_INIT, INSTANCE_INIT, METHOD_DEF, CTOR_DEF"/>
        </module>

        <module name="TodoComment">
            <property name="severity" value="info"/>
            <property name="format" value="TODO"/>
        </module>

        <module name="UpperEll"/>

        <module name="IllegalType">
            <property name="legalAbstractClassNames"
                      value="AbstractBeanDefinition, AbstractEntry"/>
            <property name="illegalClassNames"
                      value="java.util.GregorianCalendar, java.util.Vector"/>
        </module>

        <module name="DescendantToken">
            <property name="tokens" value="LITERAL_ASSERT"/>
            <property name="limitedTokens"
                      value="ASSIGN,DEC,INC,POST_DEC,POST_INC,PLUS_ASSIGN,MINUS_ASSIGN,STAR_ASSIGN,DIV_ASSIGN,MOD_ASSIGN,BSR_ASSIGN,SR_ASSIGN,SL_ASSIGN,BAND_ASSIGN,BXOR_ASSIGN,BOR_ASSIGN,METHOD_CALL"/>
            <property name="maximumNumber" value="2"/>
        </module>

        <module name="Regexp">
            <property name="format" value="[ \t]+$"/>
            <property name="illegalPattern" value="true"/>
            <property name="message" value="Trailing whitespace"/>
        </module>

        <module name="JavadocMethod"/>
    </module>
</module>
