# Contributing to Aeron

If you would like to contribute code you can do so through GitHub by sending a pull request or raising an issue with an attached patch.

When submitting code, please make every effort to follow existing conventions and style in order to keep the code as readable as possible.

[![Gitter](https://img.shields.io/gitter/room/gitterHQ/gitter.svg)](https://gitter.im/real-logic/Aeron?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge) To chat with other Aeron contributors.

## License

By contributing your code, you agree to license your contribution under the terms of the APLv2:
 
https://github.com/real-logic/aeron/blob/master/LICENSE

All files are made available under the Apache 2.0 license.

If you are adding a new file it should have the following header:

```
/*
 * Copyright 2014-2023 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
 ```