/*
 * Copyright 2014-2023 Real Logic Limited.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef AERON_TIMESTAMPS_H
#define AERON_TIMESTAMPS_H

#include <stdint.h>
#include <time.h>

void aeron_timestamps_set_timestamp(
    struct timespec *timestamp,
    int32_t offset,
    uint8_t *frame,
    size_t frame_length);

#endif // AERON_TIMESTAMPS_H
